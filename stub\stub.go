// Code generated by Thrift Compiler (0.19.0). DO NOT EDIT.

package stub

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"time"
	thrift "github.com/donnie4w/gothrift/thrift"
	"strings"
	"regexp"
)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = errors.New
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal
// (needed by validator.)
var _ = strings.Contains
var _ = regexp.MatchString

// Attributes:
//  - Code
//  - Info
type WfsError struct {
  Code *int32 `thrift:"code,1" db:"code" json:"code,omitempty"`
  Info *string `thrift:"info,2" db:"info" json:"info,omitempty"`
}

func NewWfsError() *WfsError {
  return &WfsError{}
}

var WfsError_Code_DEFAULT int32
func (p *WfsError) GetCode() int32 {
  if !p.IsSetCode() {
    return WfsError_Code_DEFAULT
  }
return *p.Code
}
var WfsError_Info_DEFAULT string
func (p *WfsError) GetInfo() string {
  if !p.IsSetInfo() {
    return WfsError_Info_DEFAULT
  }
return *p.Info
}
func (p *WfsError) IsSetCode() bool {
  return p.Code != nil
}

func (p *WfsError) IsSetInfo() bool {
  return p.Info != nil
}

func (p *WfsError) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsError)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Code = &v
}
  return nil
}

func (p *WfsError)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Info = &v
}
  return nil
}

func (p *WfsError) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "WfsError"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsError) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCode() {
    if err := oprot.WriteFieldBegin(ctx, "code", thrift.I32, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:code: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Code)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.code (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:code: ", p), err) }
  }
  return err
}

func (p *WfsError) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetInfo() {
    if err := oprot.WriteFieldBegin(ctx, "info", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:info: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Info)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.info (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:info: ", p), err) }
  }
  return err
}

func (p *WfsError) Equals(other *WfsError) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Code != other.Code {
    if p.Code == nil || other.Code == nil {
      return false
    }
    if (*p.Code) != (*other.Code) { return false }
  }
  if p.Info != other.Info {
    if p.Info == nil || other.Info == nil {
      return false
    }
    if (*p.Info) != (*other.Info) { return false }
  }
  return true
}

func (p *WfsError) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsError(%+v)", *p)
}

func (p *WfsError) Validate() error {
  return nil
}
// Attributes:
//  - Ok
//  - Error
type WfsAck struct {
  Ok bool `thrift:"ok,1,required" db:"ok" json:"ok"`
  Error *WfsError `thrift:"error,2" db:"error" json:"error,omitempty"`
}

func NewWfsAck() *WfsAck {
  return &WfsAck{}
}


func (p *WfsAck) GetOk() bool {
  return p.Ok
}
var WfsAck_Error_DEFAULT *WfsError
func (p *WfsAck) GetError() *WfsError {
  if !p.IsSetError() {
    return WfsAck_Error_DEFAULT
  }
return p.Error
}
func (p *WfsAck) IsSetError() bool {
  return p.Error != nil
}

func (p *WfsAck) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetOk bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetOk = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetOk{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Ok is not set"));
  }
  return nil
}

func (p *WfsAck)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Ok = v
}
  return nil
}

func (p *WfsAck)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  p.Error = &WfsError{}
  if err := p.Error.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Error), err)
  }
  return nil
}

func (p *WfsAck) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "WfsAck"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsAck) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ok", thrift.BOOL, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ok: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.Ok)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ok (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ok: ", p), err) }
  return err
}

func (p *WfsAck) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetError() {
    if err := oprot.WriteFieldBegin(ctx, "error", thrift.STRUCT, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:error: ", p), err) }
    if err := p.Error.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Error), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:error: ", p), err) }
  }
  return err
}

func (p *WfsAck) Equals(other *WfsAck) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Ok != other.Ok { return false }
  if !p.Error.Equals(other.Error) { return false }
  return true
}

func (p *WfsAck) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsAck(%+v)", *p)
}

func (p *WfsAck) Validate() error {
  return nil
}
// Attributes:
//  - Path
type WfsReq struct {
  Path *string `thrift:"path,1" db:"path" json:"path,omitempty"`
}

func NewWfsReq() *WfsReq {
  return &WfsReq{}
}

var WfsReq_Path_DEFAULT string
func (p *WfsReq) GetPath() string {
  if !p.IsSetPath() {
    return WfsReq_Path_DEFAULT
  }
return *p.Path
}
func (p *WfsReq) IsSetPath() bool {
  return p.Path != nil
}

func (p *WfsReq) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsReq)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Path = &v
}
  return nil
}

func (p *WfsReq) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "WfsReq"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsReq) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetPath() {
    if err := oprot.WriteFieldBegin(ctx, "path", thrift.STRING, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:path: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Path)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.path (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:path: ", p), err) }
  }
  return err
}

func (p *WfsReq) Equals(other *WfsReq) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Path != other.Path {
    if p.Path == nil || other.Path == nil {
      return false
    }
    if (*p.Path) != (*other.Path) { return false }
  }
  return true
}

func (p *WfsReq) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsReq(%+v)", *p)
}

func (p *WfsReq) Validate() error {
  return nil
}
// Attributes:
//  - Name
//  - Pwd
type WfsAuth struct {
  Name *string `thrift:"name,1" db:"name" json:"name,omitempty"`
  Pwd *string `thrift:"pwd,2" db:"pwd" json:"pwd,omitempty"`
}

func NewWfsAuth() *WfsAuth {
  return &WfsAuth{}
}

var WfsAuth_Name_DEFAULT string
func (p *WfsAuth) GetName() string {
  if !p.IsSetName() {
    return WfsAuth_Name_DEFAULT
  }
return *p.Name
}
var WfsAuth_Pwd_DEFAULT string
func (p *WfsAuth) GetPwd() string {
  if !p.IsSetPwd() {
    return WfsAuth_Pwd_DEFAULT
  }
return *p.Pwd
}
func (p *WfsAuth) IsSetName() bool {
  return p.Name != nil
}

func (p *WfsAuth) IsSetPwd() bool {
  return p.Pwd != nil
}

func (p *WfsAuth) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsAuth)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Name = &v
}
  return nil
}

func (p *WfsAuth)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Pwd = &v
}
  return nil
}

func (p *WfsAuth) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "WfsAuth"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsAuth) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetName() {
    if err := oprot.WriteFieldBegin(ctx, "name", thrift.STRING, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:name: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Name)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.name (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:name: ", p), err) }
  }
  return err
}

func (p *WfsAuth) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetPwd() {
    if err := oprot.WriteFieldBegin(ctx, "pwd", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:pwd: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Pwd)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.pwd (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:pwd: ", p), err) }
  }
  return err
}

func (p *WfsAuth) Equals(other *WfsAuth) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Name != other.Name {
    if p.Name == nil || other.Name == nil {
      return false
    }
    if (*p.Name) != (*other.Name) { return false }
  }
  if p.Pwd != other.Pwd {
    if p.Pwd == nil || other.Pwd == nil {
      return false
    }
    if (*p.Pwd) != (*other.Pwd) { return false }
  }
  return true
}

func (p *WfsAuth) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsAuth(%+v)", *p)
}

func (p *WfsAuth) Validate() error {
  return nil
}
// Attributes:
//  - Data
type WfsData struct {
  Data []byte `thrift:"data,1" db:"data" json:"data,omitempty"`
}

func NewWfsData() *WfsData {
  return &WfsData{}
}

var WfsData_Data_DEFAULT []byte

func (p *WfsData) GetData() []byte {
  return p.Data
}
func (p *WfsData) IsSetData() bool {
  return p.Data != nil
}

func (p *WfsData) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsData)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBinary(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Data = v
}
  return nil
}

func (p *WfsData) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "WfsData"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsData) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetData() {
    if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRING, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:data: ", p), err) }
    if err := oprot.WriteBinary(ctx, p.Data); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.data (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:data: ", p), err) }
  }
  return err
}

func (p *WfsData) Equals(other *WfsData) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if bytes.Compare(p.Data, other.Data) != 0 { return false }
  return true
}

func (p *WfsData) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsData(%+v)", *p)
}

func (p *WfsData) Validate() error {
  return nil
}
// Attributes:
//  - Data
//  - Name
//  - Compress
type WfsFile struct {
  Data []byte `thrift:"data,1,required" db:"data" json:"data"`
  Name string `thrift:"name,2,required" db:"name" json:"name"`
  Compress *int8 `thrift:"compress,3" db:"compress" json:"compress,omitempty"`
}

func NewWfsFile() *WfsFile {
  return &WfsFile{}
}


func (p *WfsFile) GetData() []byte {
  return p.Data
}

func (p *WfsFile) GetName() string {
  return p.Name
}
var WfsFile_Compress_DEFAULT int8
func (p *WfsFile) GetCompress() int8 {
  if !p.IsSetCompress() {
    return WfsFile_Compress_DEFAULT
  }
return *p.Compress
}
func (p *WfsFile) IsSetCompress() bool {
  return p.Compress != nil
}

func (p *WfsFile) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }

  var issetData bool = false;
  var issetName bool = false;

  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
        issetData = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
        issetName = true
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.BYTE {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  if !issetData{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Data is not set"));
  }
  if !issetName{
    return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("Required field Name is not set"));
  }
  return nil
}

func (p *WfsFile)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBinary(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Data = v
}
  return nil
}

func (p *WfsFile)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Name = v
}
  return nil
}

func (p *WfsFile)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadByte(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  temp := int8(v)
  p.Compress = &temp
}
  return nil
}

func (p *WfsFile) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "WfsFile"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsFile) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "data", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:data: ", p), err) }
  if err := oprot.WriteBinary(ctx, p.Data); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.data (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:data: ", p), err) }
  return err
}

func (p *WfsFile) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "name", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:name: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Name)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.name (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:name: ", p), err) }
  return err
}

func (p *WfsFile) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCompress() {
    if err := oprot.WriteFieldBegin(ctx, "compress", thrift.BYTE, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:compress: ", p), err) }
    if err := oprot.WriteByte(ctx, int8(*p.Compress)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.compress (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:compress: ", p), err) }
  }
  return err
}

func (p *WfsFile) Equals(other *WfsFile) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if bytes.Compare(p.Data, other.Data) != 0 { return false }
  if p.Name != other.Name { return false }
  if p.Compress != other.Compress {
    if p.Compress == nil || other.Compress == nil {
      return false
    }
    if (*p.Compress) != (*other.Compress) { return false }
  }
  return true
}

func (p *WfsFile) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsFile(%+v)", *p)
}

func (p *WfsFile) Validate() error {
  return nil
}
type WfsIface interface {
  // Parameters:
  //  - File
  Append(ctx context.Context, file *WfsFile) (_r *WfsAck, _err error)
  // Parameters:
  //  - Path
  Delete(ctx context.Context, path string) (_r *WfsAck, _err error)
  // Parameters:
  //  - Path
  //  - Newpath_
  Rename(ctx context.Context, path string, newpath string) (_r *WfsAck, _err error)
  // Parameters:
  //  - Wa
  Auth(ctx context.Context, wa *WfsAuth) (_r *WfsAck, _err error)
  // Parameters:
  //  - Path
  Get(ctx context.Context, path string) (_r *WfsData, _err error)
  Ping(ctx context.Context) (_r int8, _err error)
}

type WfsIfaceClient struct {
  c thrift.TClient
  meta thrift.ResponseMeta
}

func NewWfsIfaceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *WfsIfaceClient {
  return &WfsIfaceClient{
    c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
  }
}

func NewWfsIfaceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *WfsIfaceClient {
  return &WfsIfaceClient{
    c: thrift.NewTStandardClient(iprot, oprot),
  }
}

func NewWfsIfaceClient(c thrift.TClient) *WfsIfaceClient {
  return &WfsIfaceClient{
    c: c,
  }
}

func (p *WfsIfaceClient) Client_() thrift.TClient {
  return p.c
}

func (p *WfsIfaceClient) LastResponseMeta_() thrift.ResponseMeta {
  return p.meta
}

func (p *WfsIfaceClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
  p.meta = meta
}

// Parameters:
//  - File
func (p *WfsIfaceClient) Append(ctx context.Context, file *WfsFile) (_r *WfsAck, _err error) {
  var _args0 WfsIfaceAppendArgs
  _args0.File = file
  var _result2 WfsIfaceAppendResult
  var _meta1 thrift.ResponseMeta
  _meta1, _err = p.Client_().Call(ctx, "Append", &_args0, &_result2)
  p.SetLastResponseMeta_(_meta1)
  if _err != nil {
    return
  }
  if _ret3 := _result2.GetSuccess(); _ret3 != nil {
    return _ret3, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "Append failed: unknown result")
}

// Parameters:
//  - Path
func (p *WfsIfaceClient) Delete(ctx context.Context, path string) (_r *WfsAck, _err error) {
  var _args4 WfsIfaceDeleteArgs
  _args4.Path = path
  var _result6 WfsIfaceDeleteResult
  var _meta5 thrift.ResponseMeta
  _meta5, _err = p.Client_().Call(ctx, "Delete", &_args4, &_result6)
  p.SetLastResponseMeta_(_meta5)
  if _err != nil {
    return
  }
  if _ret7 := _result6.GetSuccess(); _ret7 != nil {
    return _ret7, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "Delete failed: unknown result")
}

// Parameters:
//  - Path
//  - Newpath_
func (p *WfsIfaceClient) Rename(ctx context.Context, path string, newpath string) (_r *WfsAck, _err error) {
  var _args8 WfsIfaceRenameArgs
  _args8.Path = path
  _args8.Newpath_ = newpath
  var _result10 WfsIfaceRenameResult
  var _meta9 thrift.ResponseMeta
  _meta9, _err = p.Client_().Call(ctx, "Rename", &_args8, &_result10)
  p.SetLastResponseMeta_(_meta9)
  if _err != nil {
    return
  }
  if _ret11 := _result10.GetSuccess(); _ret11 != nil {
    return _ret11, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "Rename failed: unknown result")
}

// Parameters:
//  - Wa
func (p *WfsIfaceClient) Auth(ctx context.Context, wa *WfsAuth) (_r *WfsAck, _err error) {
  var _args12 WfsIfaceAuthArgs
  _args12.Wa = wa
  var _result14 WfsIfaceAuthResult
  var _meta13 thrift.ResponseMeta
  _meta13, _err = p.Client_().Call(ctx, "Auth", &_args12, &_result14)
  p.SetLastResponseMeta_(_meta13)
  if _err != nil {
    return
  }
  if _ret15 := _result14.GetSuccess(); _ret15 != nil {
    return _ret15, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "Auth failed: unknown result")
}

// Parameters:
//  - Path
func (p *WfsIfaceClient) Get(ctx context.Context, path string) (_r *WfsData, _err error) {
  var _args16 WfsIfaceGetArgs
  _args16.Path = path
  var _result18 WfsIfaceGetResult
  var _meta17 thrift.ResponseMeta
  _meta17, _err = p.Client_().Call(ctx, "Get", &_args16, &_result18)
  p.SetLastResponseMeta_(_meta17)
  if _err != nil {
    return
  }
  if _ret19 := _result18.GetSuccess(); _ret19 != nil {
    return _ret19, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "Get failed: unknown result")
}

func (p *WfsIfaceClient) Ping(ctx context.Context) (_r int8, _err error) {
  var _args20 WfsIfacePingArgs
  var _result22 WfsIfacePingResult
  var _meta21 thrift.ResponseMeta
  _meta21, _err = p.Client_().Call(ctx, "Ping", &_args20, &_result22)
  p.SetLastResponseMeta_(_meta21)
  if _err != nil {
    return
  }
  return _result22.GetSuccess(), nil
}

type WfsIfaceProcessor struct {
  processorMap map[string]thrift.TProcessorFunction
  handler WfsIface
}

func (p *WfsIfaceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
  p.processorMap[key] = processor
}

func (p *WfsIfaceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
  processor, ok = p.processorMap[key]
  return processor, ok
}

func (p *WfsIfaceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
  return p.processorMap
}

func NewWfsIfaceProcessor(handler WfsIface) *WfsIfaceProcessor {

  self23 := &WfsIfaceProcessor{handler:handler, processorMap:make(map[string]thrift.TProcessorFunction)}
  self23.processorMap["Append"] = &wfsIfaceProcessorAppend{handler:handler}
  self23.processorMap["Delete"] = &wfsIfaceProcessorDelete{handler:handler}
  self23.processorMap["Rename"] = &wfsIfaceProcessorRename{handler:handler}
  self23.processorMap["Auth"] = &wfsIfaceProcessorAuth{handler:handler}
  self23.processorMap["Get"] = &wfsIfaceProcessorGet{handler:handler}
  self23.processorMap["Ping"] = &wfsIfaceProcessorPing{handler:handler}
return self23
}

func (p *WfsIfaceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
  if err2 != nil { return false, thrift.WrapTException(err2) }
  if processor, ok := p.GetProcessorFunction(name); ok {
    return processor.Process(ctx, seqId, iprot, oprot)
  }
  iprot.Skip(ctx, thrift.STRUCT)
  iprot.ReadMessageEnd(ctx)
  x24 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function " + name)
  oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
  x24.Write(ctx, oprot)
  oprot.WriteMessageEnd(ctx)
  oprot.Flush(ctx)
  return false, x24

}

type wfsIfaceProcessorAppend struct {
  handler WfsIface
}

func (p *wfsIfaceProcessorAppend) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err25 error
  args := WfsIfaceAppendArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "Append", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelCauseFunc
    ctx, cancel = context.WithCancelCause(ctx)
    defer cancel(nil)
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelCauseFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel(thrift.ErrAbandonRequest)
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := WfsIfaceAppendResult{}
  if retval, err2 := p.handler.Append(ctx, args.File); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    if errors.Is(err2, context.Canceled) {
      if err := context.Cause(ctx); errors.Is(err, thrift.ErrAbandonRequest) {
        return false, thrift.WrapTException(err)
      }
    }
    _exc26 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing Append: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "Append", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err25 = thrift.WrapTException(err2)
    }
    if err2 := _exc26.Write(ctx, oprot); _write_err25 == nil && err2 != nil {
      _write_err25 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err25 == nil && err2 != nil {
      _write_err25 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err25 == nil && err2 != nil {
      _write_err25 = thrift.WrapTException(err2)
    }
    if _write_err25 != nil {
      return false, thrift.WrapTException(_write_err25)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "Append", thrift.REPLY, seqId); err2 != nil {
    _write_err25 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err25 == nil && err2 != nil {
    _write_err25 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err25 == nil && err2 != nil {
    _write_err25 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err25 == nil && err2 != nil {
    _write_err25 = thrift.WrapTException(err2)
  }
  if _write_err25 != nil {
    return false, thrift.WrapTException(_write_err25)
  }
  return true, err
}

type wfsIfaceProcessorDelete struct {
  handler WfsIface
}

func (p *wfsIfaceProcessorDelete) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err27 error
  args := WfsIfaceDeleteArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "Delete", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelCauseFunc
    ctx, cancel = context.WithCancelCause(ctx)
    defer cancel(nil)
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelCauseFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel(thrift.ErrAbandonRequest)
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := WfsIfaceDeleteResult{}
  if retval, err2 := p.handler.Delete(ctx, args.Path); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    if errors.Is(err2, context.Canceled) {
      if err := context.Cause(ctx); errors.Is(err, thrift.ErrAbandonRequest) {
        return false, thrift.WrapTException(err)
      }
    }
    _exc28 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing Delete: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "Delete", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err27 = thrift.WrapTException(err2)
    }
    if err2 := _exc28.Write(ctx, oprot); _write_err27 == nil && err2 != nil {
      _write_err27 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err27 == nil && err2 != nil {
      _write_err27 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err27 == nil && err2 != nil {
      _write_err27 = thrift.WrapTException(err2)
    }
    if _write_err27 != nil {
      return false, thrift.WrapTException(_write_err27)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "Delete", thrift.REPLY, seqId); err2 != nil {
    _write_err27 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err27 == nil && err2 != nil {
    _write_err27 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err27 == nil && err2 != nil {
    _write_err27 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err27 == nil && err2 != nil {
    _write_err27 = thrift.WrapTException(err2)
  }
  if _write_err27 != nil {
    return false, thrift.WrapTException(_write_err27)
  }
  return true, err
}

type wfsIfaceProcessorRename struct {
  handler WfsIface
}

func (p *wfsIfaceProcessorRename) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err29 error
  args := WfsIfaceRenameArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "Rename", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelCauseFunc
    ctx, cancel = context.WithCancelCause(ctx)
    defer cancel(nil)
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelCauseFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel(thrift.ErrAbandonRequest)
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := WfsIfaceRenameResult{}
  if retval, err2 := p.handler.Rename(ctx, args.Path, args.Newpath_); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    if errors.Is(err2, context.Canceled) {
      if err := context.Cause(ctx); errors.Is(err, thrift.ErrAbandonRequest) {
        return false, thrift.WrapTException(err)
      }
    }
    _exc30 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing Rename: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "Rename", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err29 = thrift.WrapTException(err2)
    }
    if err2 := _exc30.Write(ctx, oprot); _write_err29 == nil && err2 != nil {
      _write_err29 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err29 == nil && err2 != nil {
      _write_err29 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err29 == nil && err2 != nil {
      _write_err29 = thrift.WrapTException(err2)
    }
    if _write_err29 != nil {
      return false, thrift.WrapTException(_write_err29)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "Rename", thrift.REPLY, seqId); err2 != nil {
    _write_err29 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err29 == nil && err2 != nil {
    _write_err29 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err29 == nil && err2 != nil {
    _write_err29 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err29 == nil && err2 != nil {
    _write_err29 = thrift.WrapTException(err2)
  }
  if _write_err29 != nil {
    return false, thrift.WrapTException(_write_err29)
  }
  return true, err
}

type wfsIfaceProcessorAuth struct {
  handler WfsIface
}

func (p *wfsIfaceProcessorAuth) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err31 error
  args := WfsIfaceAuthArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "Auth", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelCauseFunc
    ctx, cancel = context.WithCancelCause(ctx)
    defer cancel(nil)
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelCauseFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel(thrift.ErrAbandonRequest)
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := WfsIfaceAuthResult{}
  if retval, err2 := p.handler.Auth(ctx, args.Wa); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    if errors.Is(err2, context.Canceled) {
      if err := context.Cause(ctx); errors.Is(err, thrift.ErrAbandonRequest) {
        return false, thrift.WrapTException(err)
      }
    }
    _exc32 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing Auth: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "Auth", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err31 = thrift.WrapTException(err2)
    }
    if err2 := _exc32.Write(ctx, oprot); _write_err31 == nil && err2 != nil {
      _write_err31 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err31 == nil && err2 != nil {
      _write_err31 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err31 == nil && err2 != nil {
      _write_err31 = thrift.WrapTException(err2)
    }
    if _write_err31 != nil {
      return false, thrift.WrapTException(_write_err31)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "Auth", thrift.REPLY, seqId); err2 != nil {
    _write_err31 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err31 == nil && err2 != nil {
    _write_err31 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err31 == nil && err2 != nil {
    _write_err31 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err31 == nil && err2 != nil {
    _write_err31 = thrift.WrapTException(err2)
  }
  if _write_err31 != nil {
    return false, thrift.WrapTException(_write_err31)
  }
  return true, err
}

type wfsIfaceProcessorGet struct {
  handler WfsIface
}

func (p *wfsIfaceProcessorGet) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err33 error
  args := WfsIfaceGetArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "Get", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelCauseFunc
    ctx, cancel = context.WithCancelCause(ctx)
    defer cancel(nil)
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelCauseFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel(thrift.ErrAbandonRequest)
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := WfsIfaceGetResult{}
  if retval, err2 := p.handler.Get(ctx, args.Path); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    if errors.Is(err2, context.Canceled) {
      if err := context.Cause(ctx); errors.Is(err, thrift.ErrAbandonRequest) {
        return false, thrift.WrapTException(err)
      }
    }
    _exc34 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing Get: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "Get", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err33 = thrift.WrapTException(err2)
    }
    if err2 := _exc34.Write(ctx, oprot); _write_err33 == nil && err2 != nil {
      _write_err33 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err33 == nil && err2 != nil {
      _write_err33 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err33 == nil && err2 != nil {
      _write_err33 = thrift.WrapTException(err2)
    }
    if _write_err33 != nil {
      return false, thrift.WrapTException(_write_err33)
    }
    return true, err
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "Get", thrift.REPLY, seqId); err2 != nil {
    _write_err33 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err33 == nil && err2 != nil {
    _write_err33 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err33 == nil && err2 != nil {
    _write_err33 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err33 == nil && err2 != nil {
    _write_err33 = thrift.WrapTException(err2)
  }
  if _write_err33 != nil {
    return false, thrift.WrapTException(_write_err33)
  }
  return true, err
}

type wfsIfaceProcessorPing struct {
  handler WfsIface
}

func (p *wfsIfaceProcessorPing) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  var _write_err35 error
  args := WfsIfacePingArgs{}
  if err2 := args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "Ping", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelCauseFunc
    ctx, cancel = context.WithCancelCause(ctx)
    defer cancel(nil)
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelCauseFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel(thrift.ErrAbandonRequest)
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := WfsIfacePingResult{}
  if retval, err2 := p.handler.Ping(ctx); err2 != nil {
    tickerCancel()
    err = thrift.WrapTException(err2)
    if errors.Is(err2, thrift.ErrAbandonRequest) {
      return false, thrift.WrapTException(err2)
    }
    if errors.Is(err2, context.Canceled) {
      if err := context.Cause(ctx); errors.Is(err, thrift.ErrAbandonRequest) {
        return false, thrift.WrapTException(err)
      }
    }
    _exc36 := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing Ping: " + err2.Error())
    if err2 := oprot.WriteMessageBegin(ctx, "Ping", thrift.EXCEPTION, seqId); err2 != nil {
      _write_err35 = thrift.WrapTException(err2)
    }
    if err2 := _exc36.Write(ctx, oprot); _write_err35 == nil && err2 != nil {
      _write_err35 = thrift.WrapTException(err2)
    }
    if err2 := oprot.WriteMessageEnd(ctx); _write_err35 == nil && err2 != nil {
      _write_err35 = thrift.WrapTException(err2)
    }
    if err2 := oprot.Flush(ctx); _write_err35 == nil && err2 != nil {
      _write_err35 = thrift.WrapTException(err2)
    }
    if _write_err35 != nil {
      return false, thrift.WrapTException(_write_err35)
    }
    return true, err
  } else {
    result.Success = &retval
  }
  tickerCancel()
  if err2 := oprot.WriteMessageBegin(ctx, "Ping", thrift.REPLY, seqId); err2 != nil {
    _write_err35 = thrift.WrapTException(err2)
  }
  if err2 := result.Write(ctx, oprot); _write_err35 == nil && err2 != nil {
    _write_err35 = thrift.WrapTException(err2)
  }
  if err2 := oprot.WriteMessageEnd(ctx); _write_err35 == nil && err2 != nil {
    _write_err35 = thrift.WrapTException(err2)
  }
  if err2 := oprot.Flush(ctx); _write_err35 == nil && err2 != nil {
    _write_err35 = thrift.WrapTException(err2)
  }
  if _write_err35 != nil {
    return false, thrift.WrapTException(_write_err35)
  }
  return true, err
}


// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - File
type WfsIfaceAppendArgs struct {
  File *WfsFile `thrift:"file,1" db:"file" json:"file"`
}

func NewWfsIfaceAppendArgs() *WfsIfaceAppendArgs {
  return &WfsIfaceAppendArgs{}
}

var WfsIfaceAppendArgs_File_DEFAULT *WfsFile
func (p *WfsIfaceAppendArgs) GetFile() *WfsFile {
  if !p.IsSetFile() {
    return WfsIfaceAppendArgs_File_DEFAULT
  }
return p.File
}
func (p *WfsIfaceAppendArgs) IsSetFile() bool {
  return p.File != nil
}

func (p *WfsIfaceAppendArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsIfaceAppendArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.File = &WfsFile{}
  if err := p.File.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.File), err)
  }
  return nil
}

func (p *WfsIfaceAppendArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "Append_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsIfaceAppendArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "file", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:file: ", p), err) }
  if err := p.File.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.File), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:file: ", p), err) }
  return err
}

func (p *WfsIfaceAppendArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsIfaceAppendArgs(%+v)", *p)
}

// Attributes:
//  - Success
type WfsIfaceAppendResult struct {
  Success *WfsAck `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewWfsIfaceAppendResult() *WfsIfaceAppendResult {
  return &WfsIfaceAppendResult{}
}

var WfsIfaceAppendResult_Success_DEFAULT *WfsAck
func (p *WfsIfaceAppendResult) GetSuccess() *WfsAck {
  if !p.IsSetSuccess() {
    return WfsIfaceAppendResult_Success_DEFAULT
  }
return p.Success
}
func (p *WfsIfaceAppendResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *WfsIfaceAppendResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsIfaceAppendResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &WfsAck{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *WfsIfaceAppendResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "Append_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsIfaceAppendResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *WfsIfaceAppendResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsIfaceAppendResult(%+v)", *p)
}

// Attributes:
//  - Path
type WfsIfaceDeleteArgs struct {
  Path string `thrift:"path,1" db:"path" json:"path"`
}

func NewWfsIfaceDeleteArgs() *WfsIfaceDeleteArgs {
  return &WfsIfaceDeleteArgs{}
}


func (p *WfsIfaceDeleteArgs) GetPath() string {
  return p.Path
}
func (p *WfsIfaceDeleteArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsIfaceDeleteArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Path = v
}
  return nil
}

func (p *WfsIfaceDeleteArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "Delete_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsIfaceDeleteArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "path", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:path: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Path)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.path (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:path: ", p), err) }
  return err
}

func (p *WfsIfaceDeleteArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsIfaceDeleteArgs(%+v)", *p)
}

// Attributes:
//  - Success
type WfsIfaceDeleteResult struct {
  Success *WfsAck `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewWfsIfaceDeleteResult() *WfsIfaceDeleteResult {
  return &WfsIfaceDeleteResult{}
}

var WfsIfaceDeleteResult_Success_DEFAULT *WfsAck
func (p *WfsIfaceDeleteResult) GetSuccess() *WfsAck {
  if !p.IsSetSuccess() {
    return WfsIfaceDeleteResult_Success_DEFAULT
  }
return p.Success
}
func (p *WfsIfaceDeleteResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *WfsIfaceDeleteResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsIfaceDeleteResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &WfsAck{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *WfsIfaceDeleteResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "Delete_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsIfaceDeleteResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *WfsIfaceDeleteResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsIfaceDeleteResult(%+v)", *p)
}

// Attributes:
//  - Path
//  - Newpath_
type WfsIfaceRenameArgs struct {
  Path string `thrift:"path,1" db:"path" json:"path"`
  Newpath_ string `thrift:"newpath,2" db:"newpath" json:"newpath"`
}

func NewWfsIfaceRenameArgs() *WfsIfaceRenameArgs {
  return &WfsIfaceRenameArgs{}
}


func (p *WfsIfaceRenameArgs) GetPath() string {
  return p.Path
}

func (p *WfsIfaceRenameArgs) GetNewpath_() string {
  return p.Newpath_
}
func (p *WfsIfaceRenameArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsIfaceRenameArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Path = v
}
  return nil
}

func (p *WfsIfaceRenameArgs)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Newpath_ = v
}
  return nil
}

func (p *WfsIfaceRenameArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "Rename_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsIfaceRenameArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "path", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:path: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Path)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.path (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:path: ", p), err) }
  return err
}

func (p *WfsIfaceRenameArgs) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "newpath", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:newpath: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Newpath_)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.newpath (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:newpath: ", p), err) }
  return err
}

func (p *WfsIfaceRenameArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsIfaceRenameArgs(%+v)", *p)
}

// Attributes:
//  - Success
type WfsIfaceRenameResult struct {
  Success *WfsAck `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewWfsIfaceRenameResult() *WfsIfaceRenameResult {
  return &WfsIfaceRenameResult{}
}

var WfsIfaceRenameResult_Success_DEFAULT *WfsAck
func (p *WfsIfaceRenameResult) GetSuccess() *WfsAck {
  if !p.IsSetSuccess() {
    return WfsIfaceRenameResult_Success_DEFAULT
  }
return p.Success
}
func (p *WfsIfaceRenameResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *WfsIfaceRenameResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsIfaceRenameResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &WfsAck{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *WfsIfaceRenameResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "Rename_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsIfaceRenameResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *WfsIfaceRenameResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsIfaceRenameResult(%+v)", *p)
}

// Attributes:
//  - Wa
type WfsIfaceAuthArgs struct {
  Wa *WfsAuth `thrift:"wa,1" db:"wa" json:"wa"`
}

func NewWfsIfaceAuthArgs() *WfsIfaceAuthArgs {
  return &WfsIfaceAuthArgs{}
}

var WfsIfaceAuthArgs_Wa_DEFAULT *WfsAuth
func (p *WfsIfaceAuthArgs) GetWa() *WfsAuth {
  if !p.IsSetWa() {
    return WfsIfaceAuthArgs_Wa_DEFAULT
  }
return p.Wa
}
func (p *WfsIfaceAuthArgs) IsSetWa() bool {
  return p.Wa != nil
}

func (p *WfsIfaceAuthArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsIfaceAuthArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Wa = &WfsAuth{}
  if err := p.Wa.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Wa), err)
  }
  return nil
}

func (p *WfsIfaceAuthArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "Auth_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsIfaceAuthArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "wa", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:wa: ", p), err) }
  if err := p.Wa.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Wa), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:wa: ", p), err) }
  return err
}

func (p *WfsIfaceAuthArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsIfaceAuthArgs(%+v)", *p)
}

// Attributes:
//  - Success
type WfsIfaceAuthResult struct {
  Success *WfsAck `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewWfsIfaceAuthResult() *WfsIfaceAuthResult {
  return &WfsIfaceAuthResult{}
}

var WfsIfaceAuthResult_Success_DEFAULT *WfsAck
func (p *WfsIfaceAuthResult) GetSuccess() *WfsAck {
  if !p.IsSetSuccess() {
    return WfsIfaceAuthResult_Success_DEFAULT
  }
return p.Success
}
func (p *WfsIfaceAuthResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *WfsIfaceAuthResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsIfaceAuthResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &WfsAck{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *WfsIfaceAuthResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "Auth_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsIfaceAuthResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *WfsIfaceAuthResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsIfaceAuthResult(%+v)", *p)
}

// Attributes:
//  - Path
type WfsIfaceGetArgs struct {
  Path string `thrift:"path,1" db:"path" json:"path"`
}

func NewWfsIfaceGetArgs() *WfsIfaceGetArgs {
  return &WfsIfaceGetArgs{}
}


func (p *WfsIfaceGetArgs) GetPath() string {
  return p.Path
}
func (p *WfsIfaceGetArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsIfaceGetArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Path = v
}
  return nil
}

func (p *WfsIfaceGetArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "Get_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsIfaceGetArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "path", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:path: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Path)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.path (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:path: ", p), err) }
  return err
}

func (p *WfsIfaceGetArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsIfaceGetArgs(%+v)", *p)
}

// Attributes:
//  - Success
type WfsIfaceGetResult struct {
  Success *WfsData `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewWfsIfaceGetResult() *WfsIfaceGetResult {
  return &WfsIfaceGetResult{}
}

var WfsIfaceGetResult_Success_DEFAULT *WfsData
func (p *WfsIfaceGetResult) GetSuccess() *WfsData {
  if !p.IsSetSuccess() {
    return WfsIfaceGetResult_Success_DEFAULT
  }
return p.Success
}
func (p *WfsIfaceGetResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *WfsIfaceGetResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsIfaceGetResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &WfsData{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *WfsIfaceGetResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "Get_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsIfaceGetResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *WfsIfaceGetResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsIfaceGetResult(%+v)", *p)
}

type WfsIfacePingArgs struct {
}

func NewWfsIfacePingArgs() *WfsIfacePingArgs {
  return &WfsIfacePingArgs{}
}

func (p *WfsIfacePingArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    if err := iprot.Skip(ctx, fieldTypeId); err != nil {
      return err
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsIfacePingArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "Ping_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsIfacePingArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsIfacePingArgs(%+v)", *p)
}

// Attributes:
//  - Success
type WfsIfacePingResult struct {
  Success *int8 `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewWfsIfacePingResult() *WfsIfacePingResult {
  return &WfsIfacePingResult{}
}

var WfsIfacePingResult_Success_DEFAULT int8
func (p *WfsIfacePingResult) GetSuccess() int8 {
  if !p.IsSetSuccess() {
    return WfsIfacePingResult_Success_DEFAULT
  }
return *p.Success
}
func (p *WfsIfacePingResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *WfsIfacePingResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.BYTE {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *WfsIfacePingResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadByte(ctx); err != nil {
  return thrift.PrependError("error reading field 0: ", err)
} else {
  temp := int8(v)
  p.Success = &temp
}
  return nil
}

func (p *WfsIfacePingResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "Ping_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *WfsIfacePingResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.BYTE, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := oprot.WriteByte(ctx, int8(*p.Success)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.success (0) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *WfsIfacePingResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("WfsIfacePingResult(%+v)", *p)
}


