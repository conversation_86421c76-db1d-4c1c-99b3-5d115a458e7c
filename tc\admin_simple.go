//go:build nowebp
// +build nowebp

// Copyright (c) 2023, donnie <<EMAIL>>
// All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.
//
// github.com/donnie4w/wfs

package tc

import (
	"fmt"

	. "github.com/donnie4w/gofer/hashmap"
	tldbKs "github.com/donnie4w/gofer/keystore"
	goutil "github.com/donnie4w/gofer/util"
	"github.com/donnie4w/tlnet"

	// . "github.com/donnie4w/wfs/keystore" // 移除未使用的导入
	_ "github.com/donnie4w/wfs/stub"
	"github.com/donnie4w/wfs/sys"
	"github.com/donnie4w/wfs/util"
)

func init() {
	sys.Serve.Put(4, adminservice)
	sys.Serve.Put(5, clientservice)
}

type adminService struct {
	isClose bool
	tln     *tlnet.Tlnet
}

var adminservice = &adminService{false, tlnet.NewTlnet()}

func (t *adminService) Serve() (err error) {
	// 在nowebp版本中，不初始化图像处理
	defer util.Recover()
	if sys.WEBADDR != "" {
		err = t._serve(sys.WEBADDR, sys.Conf.Ssl_crt, sys.Conf.Ssl_crt_key)
	}
	return
}

func (t *adminService) Close() (err error) {
	defer util.Recover()
	if sys.WEBADDR != "" {
		t.isClose = true
		err = t.tln.Close()
	}
	return
}

func (t *adminService) _serve(addr, crt, key string) (err error) {
	defer util.Recover()

	// 基本的Web管理接口
	t.tln.Handle("/", func(hc *tlnet.HttpContext) {
		hc.Writer().Header().Set("Content-Type", "text/html; charset=utf-8")
		hc.Writer().Write([]byte(`
<!DOCTYPE html>
<html>
<head>
    <title>WFS Management</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>WFS File Storage System</h1>
    <h2>Management Interface (Simplified Version)</h2>
    <p>WebP image processing is disabled in this build.</p>
    
    <h3>Available APIs:</h3>
    <ul>
        <li><a href="/admin/exist?path=test/file.txt">/admin/exist?path=test/file.txt</a> - Check if file exists</li>
        <li><a href="/admin/status">/admin/status</a> - System status</li>
        <li><a href="/admin/info">/admin/info</a> - System information</li>
    </ul>
    
    <h3>Thrift Interface:</h3>
    <p>Thrift server is running on port 6802 with the new Exist interface available.</p>

    <h3>File Operations:</h3>
    <p>Use HTTP headers for authentication:</p>
    <ul>
        <li>username: admin</li>
        <li>password: 123</li>
    </ul>
</body>
</html>`))
	})

	// Exist接口的Web处理器
	t.tln.Handle("/admin/exist", func(hc *tlnet.HttpContext) {
		defer util.Recover()
		if hc.Request().Method == "OPTIONS" {
			hc.Writer().Header().Set("Access-Control-Allow-Origin", "*")
			hc.Writer().Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			hc.Writer().Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
			hc.Writer().WriteHeader(200)
			return
		}

		path := hc.Request().URL.Query().Get("path")
		if path == "" {
			hc.Writer().Header().Set("Content-Type", "application/json")
			hc.Writer().Write([]byte(`{"error": "path parameter required"}`))
			return
		}

		data := sys.GetData(path)
		exists := len(data) > 0
		size := len(data)

		response := fmt.Sprintf(`{"exists": %t, "size": %d, "path": "%s"}`, exists, size, path)
		hc.Writer().Header().Set("Content-Type", "application/json")
		hc.Writer().Write([]byte(response))
	})

	// 系统状态接口
	t.tln.Handle("/admin/status", func(hc *tlnet.HttpContext) {
		defer util.Recover()
		hc.Writer().Header().Set("Content-Type", "application/json")
		hc.Writer().Write([]byte(`{
			"status": "running",
			"version": "WFS with Exist interface",
			"webp_support": false,
			"thrift_port": 6802,
			"web_port": 6801
		}`))
	})

	// 系统信息接口
	t.tln.Handle("/admin/info", func(hc *tlnet.HttpContext) {
		defer util.Recover()
		hc.Writer().Header().Set("Content-Type", "application/json")
		response := fmt.Sprintf(`{
			"system": "WFS File Storage System",
			"features": ["file_storage", "thrift_api", "exist_interface"],
			"disabled_features": ["webp_processing", "image_manipulation"],
			"build_tags": ["nowebp"]
		}`)
		hc.Writer().Write([]byte(response))
	})

	// 文件上传接口（简化版）
	t.tln.Handle("/admin/upload", func(hc *tlnet.HttpContext) {
		defer util.Recover()
		if hc.Request().Method != "POST" {
			hc.Writer().Write([]byte("Method not allowed"))
			return
		}

		// 简化的上传处理
		hc.Writer().Header().Set("Content-Type", "application/json")
		hc.Writer().Write([]byte(`{"message": "Upload functionality available via Thrift interface"}`))
	})

	if crt != "" && key != "" {
		err = t.tln.HttpsStart(addr, crt, key)
	} else {
		err = t.tln.HttpStart(addr)
	}
	return
}

// 简化的认证函数
func isAuth(hc *tlnet.HttpContext) bool {
	name := hc.Request().Header.Get("username")
	pwd := hc.Request().Header.Get("password")
	// 简化的认证逻辑
	return name == "admin" && pwd == "123"
}

// 为了兼容性，定义一些必要的变量和函数
var sessionMap = NewMapL[string, *tldbKs.UserBean]()

func authAccount(hc *tlnet.HttpContext) bool {
	return isAuth(hc)
}

func getSessionid() string {
	return fmt.Sprint("t", goutil.CRC32(goutil.Int64ToBytes(sys.UUID)))
}

func getLangId() string {
	return fmt.Sprint("l", goutil.CRC32(goutil.Int64ToBytes(sys.UUID)))
}

// 简化的信号处理
func signalListen() {
	defer util.Recover()
	// 在简化版本中不处理信号
}
