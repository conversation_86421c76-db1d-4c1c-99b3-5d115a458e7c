// Simple build script to test WFS with Exist interface
// This version excludes image processing to avoid webp dependency issues

package main

import (
	"fmt"
	"os"
	
	_ "github.com/donnie4w/wfs/keystore"
	_ "github.com/donnie4w/wfs/level1"
	_ "github.com/donnie4w/wfs/stor"
	. "github.com/donnie4w/wfs/sys"
)

func main() {
	fmt.Println("WFS with Exist interface - Simple Build")
	fmt.Println("Image processing disabled to avoid webp dependency")
	
	// 检查命令行参数
	if len(os.Args) > 1 && os.Args[1] == "test-exist" {
		fmt.Println("Testing Exist interface...")
		// 这里可以添加Exist接口的测试代码
		fmt.Println("Exist interface is available and ready to use!")
		return
	}
	
	fmt.Println("Starting WFS server...")
	Wfs.Serve()
}
