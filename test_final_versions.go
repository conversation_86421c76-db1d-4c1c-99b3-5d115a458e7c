// Final test for all compiled WFS versions with Exist interface
package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/donnie4w/gothrift/thrift"
	"github.com/donnie4w/wfs/stub"
)

func main() {
	fmt.Println("WFS Final Version Test Suite")
	fmt.Println("============================")
	fmt.Println("Testing all compiled versions with new Exist interface")
	fmt.Println()

	// 测试各个版本的可执行文件
	versions := []string{
		"wfs_core.exe",
		"wfs_simple_web.exe", 
		"wfs_full_nowebp.exe",
	}

	for _, version := range versions {
		fmt.Printf("Testing %s...\n", version)
		testVersion(version)
		fmt.Println()
	}

	fmt.Println("✅ All version tests completed!")
}

func testVersion(version string) {
	fmt.Printf("  Version: %s\n", version)
	
	// 测试Thrift接口
	if testThriftInterface() {
		fmt.Println("  ✅ Thrift interface working")
	} else {
		fmt.Println("  ❌ Thrift interface failed")
	}
	
	// 测试Web接口（如果适用）
	if version != "wfs_core.exe" {
		if testWebInterface() {
			fmt.Println("  ✅ Web interface working")
		} else {
			fmt.Println("  ❌ Web interface failed")
		}
	} else {
		fmt.Println("  ⚪ Web interface not available (core version)")
	}
}

func testThriftInterface() bool {
	// 创建Thrift客户端连接
	transport, err := thrift.NewTSocketTimeout("localhost:6802", time.Second*5, time.Second*5)
	if err != nil {
		return false
	}
	
	if err := transport.Open(); err != nil {
		return false
	}
	defer transport.Close()
	
	protocol := thrift.NewTCompactProtocol(transport)
	client := stub.NewWfsIfaceClient(thrift.NewTStandardClient(protocol, protocol))
	
	ctx := context.Background()
	
	// 测试Ping
	if _, err := client.Ping(ctx); err != nil {
		return false
	}
	
	// 测试Exist接口
	if result, err := client.Exist(ctx, "test/nonexistent.txt"); err != nil {
		return false
	} else {
		// 验证返回结果格式正确
		if result.GetExists() == false {
			return true // 文件不存在是正确的
		}
	}
	
	return false
}

func testWebInterface() bool {
	// 测试Web接口是否可访问
	client := &http.Client{Timeout: time.Second * 5}
	
	// 测试主页
	resp, err := client.Get("http://localhost:6801/")
	if err != nil {
		return false
	}
	defer resp.Body.Close()
	
	return resp.StatusCode == 200
}
