// Test client for WFS Exist interface
package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/donnie4w/gothrift/thrift"
	"github.com/donnie4w/wfs/stub"
)

func main() {
	fmt.Println("WFS Exist Interface Test Client")
	
	// 连接到WFS服务器
	transport, err := thrift.NewTSocketTimeout("localhost:6802", time.Second*10)
	if err != nil {
		log.Printf("Error creating transport: %v", err)
		return
	}
	
	if err := transport.Open(); err != nil {
		log.Printf("Error opening transport: %v", err)
		return
	}
	defer transport.Close()
	
	// 创建协议和客户端
	protocol := thrift.NewTCompactProtocol(transport)
	client := stub.NewWfsIfaceClient(thrift.NewTStandardClient(protocol, protocol))
	
	ctx := context.Background()
	
	// 测试Ping
	fmt.Println("Testing Ping...")
	if pingResult, err := client.Ping(ctx); err != nil {
		log.Printf("Ping failed: %v", err)
		return
	} else {
		fmt.Printf("Ping successful: %d\n", pingResult)
	}
	
	// 测试认证
	fmt.Println("Testing Auth...")
	auth := &stub.WfsAuth{}
	username := "admin"
	password := "123"
	auth.Name = &username
	auth.Pwd = &password
	
	if authResult, err := client.Auth(ctx, auth); err != nil {
		log.Printf("Auth failed: %v", err)
		return
	} else {
		fmt.Printf("Auth result: ok=%v\n", authResult.GetOk())
		if !authResult.GetOk() {
			fmt.Println("Authentication failed, but continuing with Exist test...")
		}
	}
	
	// 测试Exist接口
	fmt.Println("Testing Exist interface...")
	testPaths := []string{
		"test/file1.txt",
		"nonexistent/file.txt",
		"",
	}
	
	for _, path := range testPaths {
		fmt.Printf("Checking existence of: '%s'\n", path)
		if existResult, err := client.Exist(ctx, path); err != nil {
			log.Printf("Exist failed for path '%s': %v", path, err)
		} else {
			fmt.Printf("  Exists: %v", existResult.GetExists())
			if existResult.IsSetSize() {
				fmt.Printf(", Size: %d bytes", existResult.GetSize())
			}
			fmt.Println()
		}
	}
	
	fmt.Println("Test completed!")
}
