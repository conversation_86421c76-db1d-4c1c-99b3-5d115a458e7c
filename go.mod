module github.com/donnie4w/wfs

go 1.22.4

require (
	github.com/donnie4w/gofer v0.1.5
	github.com/donnie4w/gothrift v0.0.3
	github.com/donnie4w/simplelog v0.1.1
	github.com/donnie4w/tlnet v0.1.0
	github.com/hashicorp/golang-lru/v2 v2.0.7
	github.com/shirou/gopsutil/v3 v3.24.5
	github.com/syndtr/goleveldb v1.0.0
	github.com/yuin/goldmark v1.7.4
	golang.org/x/net v0.29.0
	google.golang.org/protobuf v1.34.2
)

require (
	github.com/apache/thrift v0.21.0 // indirect
	github.com/chai2010/webp v1.1.1 // indirect
	github.com/disintegration/imaging v1.6.2 // indirect
	github.com/donnie4w/ico v0.0.1 // indirect
	github.com/edsrzf/mmap-go v1.1.0 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/btree v1.1.3 // indirect
	github.com/klauspost/compress v1.17.10 // indirect
	github.com/lufia/plan9stats v0.0.0-20211012122336-39d0f177ccd0 // indirect
	github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c // indirect
	github.com/shoenig/go-m1cpu v0.1.6 // indirect
	github.com/tklauser/go-sysconf v0.3.12 // indirect
	github.com/tklauser/numcpus v0.6.1 // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	golang.org/x/exp v0.0.0-20240909161429-701f63a606c0 // indirect
	golang.org/x/image v0.20.0 // indirect
	golang.org/x/sys v0.25.0 // indirect
)
