package main

import (
	"fmt"

	"github.com/donnie4w/wfs/stub"
)

func main() {
	// 创建一个简单的测试来验证Exist接口是否正确定义
	fmt.Println("Testing WFS Exist interface...")

	// 测试WfsExist结构
	exist := &stub.WfsExist{
		Exists: true,
	}
	size := int64(1024)
	exist.Size = &size

	fmt.Printf("WfsExist created: exists=%v, size=%v\n", exist.GetExists(), exist.GetSize())

	// 测试接口定义
	var iface stub.WfsIface
	if iface == nil {
		fmt.Println("WfsIface interface is properly defined")
	}

	fmt.Println("Test completed successfully!")
}
